/*==========================================================================
 * 文件名称：User_Task.c
 * 版权信息：米醋电子工作室
 * 重构日期：2025-01-XX
 * 重构版本：v2.0 - 降落功能优化版
 * 编码格式：UTF-8
 *
 * 重构说明：
 * 本次重构解决了降落功能中的变量命名混乱和状态管理问题，主要改进：
 *
 * 1. 【核心问题解决】降落超时后不能正确执行FC_Lock()
 *    - 原因：execute_landing_sequence只执行一次，定时器停止累加
 *    - 解决：实现持续执行机制，确保定时器累加到超时阈值
 *
 * 2. 【权限管理机制】满足遥控器开关约束条件
 *    - 约束：默认状态（开关上位）飞机未起飞时不执行降落
 *    - 解决：开关下拨授权机制，只有下拨过才能获得降落权限
 *
 * 3. 【状态管理优化】引入清晰的状态枚举
 *    - 原有：混乱的can_land_f变量承担双重职责
 *    - 改进：landing_state_t枚举 + switch_ever_pulled_down权限标志
 *
 * 4. 【变量命名优化】提升代码可读性和可维护性
 *    - can_land_f → switch_ever_pulled_down + landing_state
 *    - can_up_f → mission_enabled_flag，内部使用mission_active
 *
 * 5. 【系统兼容性】保持与现有系统的完全兼容
 *    - zigbee.c双控制源机制正常工作
 *    - execute_mission_sequence函数接口不变
===========================================================================*/

#include "User_Task.h"
#include "Drv_RcIn.h"
#include "LX_FcFunc.h"
#include "mid360.h"
#include "PID.h"
#include "Ano_Math.h"
#include "AnoPTv8FrameFactory.h"
#include "AnoPTv8.h"
#include "Maixcam.h"
#include "zigbee.h"
#include "path_storage.h"  // 返航路径查找函数
#include <stdio.h>  // 添加sprintf支持  // 野生动物巡查路径规划模块
#include "tofmini.h"
#include "Drv_PwmOut.h"


// ================== LED PWM控制相关定义 ==================
static u16 led_timer_ms = 0;      // LED定时器
#define LED_ON_TIME_MS 500         // LED开启时间0.5秒
/**
 * @brief LED PWM控制函数
 * @note 在10ms调度器中调用，当LED_f=1时控制LED以100%占空比输出0.5秒
 */
 void LED_PWM_Control(void)
{
    if (LED_f == 1) {
        // LED控制标志被设置，开始PWM输出
        TIM8->CCR4 = 8000;  // 通道5，100%占空比

        // 累加定时器
        led_timer_ms += 10;  // 10ms调度器，每次增加10ms

        // 检查是否达到0.5秒
        if (led_timer_ms >= LED_ON_TIME_MS) {
            // 0.5秒时间到，关闭PWM输出并清除标志
            TIM8->CCR4 = 0;  // 关闭PWM输出
            LED_f = 0;           // 清除LED控制标志
            led_timer_ms = 0;    // 重置定时器
        }
    } else {
        // LED控制标志未设置，确保PWM输出关闭
        TIM8->CCR4 = 0;  // 确保PWM输出关闭
        led_timer_ms = 0;        // 重置定时器
    }
}
// ================== 激光笔 ==================
void jiguang(u8 a,u8 b,u8 c)
{
	if(a){
		TIM8->CCR3 =8000;
	}else{
		TIM8->CCR3 =0;
	}
	
	if(b){
		 TIM5->CCR4 = 8000;
	}else{
		TIM5->CCR4 =0;
	}
	
	if(c){
		TIM5->CCR3 = 8000;
	}else{
		TIM5->CCR3 =0;
	}
}





bool check_mission_timeout(void);
int get_next_patrol_point(void);
void mark_patrol_point_completed(int index);
bool is_patrol_complete(void);
void get_patrol_statistics(int *completed, int *total, uint32_t *elapsed_ms);
int find_work_pos_index_by_position_code(u8 position_code);
static void execute_landing_sequence_v2(u16 *timer_ms, landing_state_t *state);
static void execute_mission_state_machine(void);
static void set_target_position(s16 x, s16 y, s16 z, s16 yaw);
static void enable_position_control(bool x, bool y, bool z, bool yaw);



// ================== 45度角降落函数声明 ==================
static bool start_45deg_descent(void);
static bool control_45deg_descent(bool return_navigation_active);

// ================== 遥控器通道常量定义 ==================
#define RC_CHANNEL_LANDING_MIN    800    // 降落命令最小值
#define RC_CHANNEL_LANDING_MAX    1200   // 降落命令最大值
#define RC_CHANNEL_MISSION_MIN    1700   // 任务命令最小值
#define RC_CHANNEL_MISSION_MAX    2000   // 任务命令最大值
#define RC_CHANNEL_DADIAN_MIN     1700   // 打点命令最小值
#define RC_CHANNEL_DADIAN_MAX     2000   // 打点命令最大值
#define RC_LANDING_TIMEOUT_MS     4000   // 降落超时时间(毫秒)
#define RC_TASK_INTERVAL_MS       20     // 任务调用间隔(毫秒)
#define RC_MISSION_DELAY_MS       60 // 任务延时时间(毫秒)
#define RC_HOVER_TIME_MS          4000   // 悬停时间(毫秒)
#define RC_POSITION_THRESHOLD     5      // 位置到达阈值
#define RC_YAW_THRESHOLD          3      // 偏航角到达阈值

// ================== 工作点和任务相关常量 ==================
#define WORK_POINT_COUNT          63      // 工作点数量 (7×9网格)
#define WORK_POINT_ARRAY_SIZE     63     // 工作点数组大小
#define PATH_PLANNER_SUCCESS      0       // 路径规划成功返回值
#define WORK_HEIGHT_HIGH          100    // 高空作业高度(cm)
#define WORK_HEIGHT_LOW           35     // 低空作业高度(cm)
#define HOME_HEIGHT               100-17    // 起始点高度(cm)
#define LANDING_HEIGHT            20     // 降落高度(cm)
#define TURN_ANGLE_175            175    // 转向角度175度
#define TURN_ANGLE_0              0      // 转向角度0度
#define RADAR_DATA_THRESHOLD      1000   // 雷达数据异常阈值(cm)



// ================== 变量声明 ==================
u8 BEEP_flag;
u8 yuyin_flag = 0;

u8 mission_step;
static u8 dadian_f = 0;
u8 dadian_cnt = 0;
//u8 task_times = 0;
u8 LED_f = 0;                      // LED控制标志

// ================== 野生动物巡查相关变量 ==================
int current_patrol_index = 0;                  // 当前巡查点索引（全局变量，供zigbee.c访问）
static int patrol_points_completed = 0;        // 已完成巡查点数量
u32 mission_start_time_ms = 0;                 // 任务开始时间（全局变量，供zigbee.c访问）
static bool patrol_point_status[WORK_POINT_ARRAY_SIZE] = {false}; // 巡查点完成状态

// 动物数据发送状态跟踪 - 基于position_code
#define MAX_SENT_POSITIONS 70  // 最多63个位置点
static u8 sent_position_codes[MAX_SENT_POSITIONS]; // 已发送动物数据的position_code列表
static int sent_count = 0; // 已发送position_code的数量

// ================== 返航路径相关变量 ==================
static u8 current_return_path_data[MAX_RETURN_LENGTH]; // 返航路径数据存储
static int current_return_path_length = 0;        // 返航路径长度
static int current_return_step = 0;               // 当前返航步骤索引
static bool return_path_loaded = false;           // 返航路径加载状态

// ================== 45°角降落相关变量 ==================
static float descent_start_height = 0;         // 降落开始高度
static float descent_distance = 0;             // 降落距离
static float descent_angle = 45.0f;            // 降落角度
static float descent_speed = 20.0f;            // 降落速度 cm/s

/**
 * @brief 全局降落上下文管理器
 * @note 替代原有的分散静态变量和全局标志位，实现统一的降落状态管理
 *
 * 初始化状态：
 * - switch_ever_pulled_down: false (未获得降落权限)
 * - state: LANDING_STATE_IDLE (空闲状态)
 * - timer_ms: 0 (定时器初始值)
 *
 * 使用说明：
 * - 此变量替代了原有的 landing_timer_reset_flag 全局标志位
 * - 通过直接操作结构体成员，消除了异步标志位通信的复杂性
 * - 所有降落相关的状态变更都通过此上下文进行管理
 */
static landing_context_t g_landing_context = {
    .switch_ever_pulled_down = false,
    .state = LANDING_STATE_IDLE,
    .timer_ms = 0
};

// ================== 工作点相关 ==================
s16 home_pos[4];

// ================== 预计算路径相关 ==================
static u8 precomputed_path_buffer[MAX_PATH_LENGTH]; // 预计算路径缓冲区
static const u8* current_path_ptr = NULL; // 当前路径指针（指向Flash中的路径数据）
static int precomputed_path_length = 0;   // 预计算路径长度
static int current_path_index = 0;        // 当前路径执行索引

// 【新增】巡查路径坐标数组 - 直接存储所有巡查点的坐标，避免重复查找
static s16 patrol_path_coords[MAX_PATH_LENGTH][4];  // 巡查路径坐标数组 [x, y, z, yaw]
static int patrol_path_length = 0;             // 巡查路径实际长度
static int current_patrol_step = 0;            // 当前巡查步骤索引

//理想状态 位置不漂的情况下的工作点
//work_pos[i][0-5] = {x, y, z, yaw, position_code, status}
//字段定义：
//  [0] x坐标 (cm)
//  [1] y坐标 (cm)
//  [2] z高度 (cm)
//  [3] yaw角度 (度)
//  [4] position_code (位置编码，行号×10+列号)
//  [5] status (状态标记，0=正常，1=禁飞区)
s16 work_pos[WORK_POINT_ARRAY_SIZE][6] = {
        // A9行 (Y=0): A9B1 到 A9B7 - position_code = 9×10+列号
        {0, 0, 110, 0, 91, 0},      //0 A9B1
        {50, 0, 110, 0, 92, 0},     //1 A9B2
        {100, 0, 110, 0, 93, 0},    //2 A9B3
        {150, 0, 110, 0, 94, 0},    //3 A9B4
        {200, 0, 110, 0, 95, 0},    //4 A9B5
        {250, 0, 110, 0, 96, 0},    //5 A9B6
        {300, 0, 110, 0, 97, 0},    //6 A9B7

        // A8行 (Y=50): A8B1 到 A8B7 - position_code = 8×10+列号
        {0, 50, 110, 0, 81, 0},     //7 A8B1
        {50, 50, 110, 0, 82, 0},    //8 A8B2
        {100, 50, 110, 0, 83, 0},   //9 A8B3
        {150, 50, 110, 0, 84, 0},   //10 A8B4
        {200, 50, 110, 0, 85, 0},   //11 A8B5
        {250, 50, 110, 0, 86, 0},   //12 A8B6
        {300, 50, 110, 0, 87, 0},   //13 A8B7

        // A7行 (Y=100): A7B1 到 A7B7 - position_code = 7×10+列号
        {0, 100, 110, 0, 71, 0},    //14 A7B1
        {50, 100, 110, 0, 72, 0},   //15 A7B2
        {100, 100, 110, 0, 73, 0},  //16 A7B3
        {150, 100, 110, 0, 74, 0},  //17 A7B4
        {200, 100, 110, 0, 75, 0},  //18 A7B5
        {250, 100, 110, 0, 76, 0},  //19 A7B6
        {300, 100, 110, 0, 77, 0},  //20 A7B7

        // A6行 (Y=150): A6B1 到 A6B7 - position_code = 6×10+列号
        {0, 150, 110, 0, 61, 0},    //21 A6B1
        {50, 150, 110, 0, 62, 0},   //22 A6B2
        {100, 150, 110, 0, 63, 0},  //23 A6B3
        {150, 150, 110, 0, 64, 0},  //24 A6B4
        {200, 150, 110, 0, 65, 0},  //25 A6B5
        {250, 150, 110, 0, 66, 0},  //26 A6B6
        {300, 150, 110, 0, 67, 0},  //27 A6B7

        // A5行 (Y=200): A5B1 到 A5B7 - position_code = 5×10+列号
        {0, 200, 110, 0, 51, 0},    //28 A5B1
        {50, 200, 110, 0, 52, 0},   //29 A5B2
        {100, 200, 110, 0, 53, 0},  //30 A5B3
        {150, 200, 110, 0, 54, 0},  //31 A5B4
        {200, 200, 110, 0, 55, 0},  //32 A5B5
        {250, 200, 110, 0, 56, 0},  //33 A5B6
        {300, 200, 110, 0, 57, 0},  //34 A5B7

        // A4行 (Y=250): A4B1 到 A4B7 - position_code = 4×10+列号
        {0, 250, 110, 0, 41, 0},    //35 A4B1
        {50, 250, 110, 0, 42, 0},   //36 A4B2
        {100, 250, 110, 0, 43, 0},  //37 A4B3
        {150, 250, 110, 0, 44, 0},  //38 A4B4
        {200, 250, 110, 0, 45, 0},  //39 A4B5
        {250, 250, 110, 0, 46, 0},  //40 A4B6
        {300, 250, 110, 0, 47, 0},  //41 A4B7

        // A3行 (Y=300): A3B1 到 A3B7 - position_code = 3×10+列号
        {0, 300, 110, 0, 31, 0},    //42 A3B1
        {50, 300, 110, 0, 32, 0},   //43 A3B2
        {100, 300, 110, 0, 33, 0},  //44 A3B3
        {150, 300, 110, 0, 34, 0},  //45 A3B4
        {200, 300, 110, 0, 35, 0},  //46 A3B5
        {250, 300, 110, 0, 36, 0},  //47 A3B6
        {300, 300, 110, 0, 37, 0},  //48 A3B7

        // A2行 (Y=350): A2B1 到 A2B7 - position_code = 2×10+列号
        {0, 350, 110, 0, 21, 0},    //49 A2B1
        {50, 350, 110, 0, 22, 0},   //50 A2B2
        {100, 350, 110, 0, 23, 0},  //51 A2B3
        {150, 350, 110, 0, 24, 0},  //52 A2B4
        {200, 350, 110, 0, 25, 0},  //53 A2B5
        {250, 350, 110, 0, 26, 0},  //54 A2B6
        {300, 350, 110, 0, 27, 0},  //55 A2B7

        // A1行 (Y=400): A1B1 到 A1B7 - position_code = 1×10+列号
        {0, 400, 110, 0, 11, 0},    //56 A1B1
        {50, 400, 110, 0, 12, 0},   //57 A1B2
        {100, 400, 110, 0, 13, 0},  //58 A1B3
        {150, 400, 110, 0, 14, 0},  //59 A1B4
        {200, 400, 110, 0, 15, 0},  //60 A1B5
        {250, 400, 110, 0, 16, 0},  //61 A1B6
        {300, 400, 110, 0, 17, 0},  //62 A1B7
};

s16 actual_work_pos[WORK_POINT_ARRAY_SIZE][4];//加上漂移值后，实际工作的点



// ================== 内联辅助函数 ==================
/**
 * @brief 检查遥控器信号是否有效
 * @return true: 信号有效, false: 信号无效
 */
static inline bool is_rc_signal_valid(void) {
    return (rc_in.no_signal == 0);
}

/**
 * @brief 检查是否为降落命令
 * @param ch_value 通道值
 * @return true: 是降落命令, false: 不是降落命令
 */
static inline bool is_landing_command(uint16_t ch_value) {
    return (ch_value > RC_CHANNEL_LANDING_MIN && ch_value < RC_CHANNEL_LANDING_MAX);
}

/**
 * @brief 检查是否为任务命令
 * @param ch_value 通道值
 * @return true: 是任务命令, false: 不是任务命令
 */
static inline bool is_mission_command(uint16_t ch_value) {
    return (ch_value > RC_CHANNEL_MISSION_MIN && ch_value < RC_CHANNEL_MISSION_MAX);
}

/**
 * @brief 检查是否为打点下拉命令
 * @param ch_value 通道值
 * @return true: 是打点下拉命令, false: 不是打点下拉命令
 */
static inline bool is_dadian_down_command(uint16_t ch_value) {
    return (ch_value > RC_CHANNEL_DADIAN_MIN && ch_value < RC_CHANNEL_DADIAN_MAX);
}

/**
 * @brief 检查XY位置是否到达目标
 * @return true: 位置已到达, false: 位置未到达
 */
bool is_position_reached(void) {
    return (ABS(PID_V[0]) < RC_POSITION_THRESHOLD && ABS(PID_V[1]) < RC_POSITION_THRESHOLD);
}

/**
 * @brief 检查偏航角是否到达目标
 * @return true: 偏航角已到达, false: 偏航角未到达
 */
bool is_yaw_reached(void) {
    return (ABS(PID_V[3]) < RC_YAW_THRESHOLD);
}

/**
 * @brief 检查CAM数据是否到达目标
 * @return true: CAM数据已到达, false: CAM数据未到达
 */
static inline bool is_CAM_reached(void) {
    return (ABS(120-maixcam.x) < 10  && ABS(90-maixcam.y) < 10);
}

/**
 * @brief 检查Z轴位置是否到达目标
 * @return true: Z轴位置已到达, false: Z轴位置未到达
 * @note 用于降落过程中检测是否到达目标降落高度
 */
 inline bool is_z_position_reached(void) {
    return (ABS(PID_V[2]) < RC_POSITION_THRESHOLD);
}


/**
 * @brief 检查工作点是否有效(已初始化)
 * @param index 工作点索引
 * @return true: 工作点有效, false: 工作点无效
 */
static inline bool is_work_point_valid(int index) {
    return (work_pos[index][0] != 0 || work_pos[index][1] != 0 ||
            work_pos[index][2] != 0 || work_pos[index][3] != 0);
}

/**
 * @brief 检查雷达数据是否异常
 * @param x_pos X坐标位置
 * @param y_pos Y坐标位置
 * @return true: 数据异常, false: 数据正常
 */
static inline bool is_radar_data_abnormal(s16 x_pos, s16 y_pos) {
    return (ABS(x_pos) > RADAR_DATA_THRESHOLD || ABS(y_pos) > RADAR_DATA_THRESHOLD);
}

// ================== 工作点计算函数 ==================
/**
 * @brief 计算实际工作点坐标（应用漂移补偿）
 * @note 将理想工作点坐标加上起始点漂移，得到实际工作点坐标
 */
static void calculate_actual_work_positions(void)
{
    for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++) {
        if (is_work_point_valid(i)) {
            // 应用漂移到x和y坐标，z和yaw保持不变
            actual_work_pos[i][0] = work_pos[i][0] + home_pos[0];
            actual_work_pos[i][1] = work_pos[i][1] + home_pos[1];
            actual_work_pos[i][2] = work_pos[i][2];
            actual_work_pos[i][3] = work_pos[i][3];
        } else {
            // 清零未初始化的工作点（使用循环替代memset）
            for (int j = 0; j < 4; j++) {
                actual_work_pos[i][j] = 0;
            }
        }
    }
}
/**
 * @brief 将预计算路径转换为坐标数组
 * @param patrol_path_length 输出巡航路径长度的指针
 * @param patrol_path_coords 输出巡航路径坐标数组的指针
 * @note 将position_code序列转换为actual_work_pos坐标，避免运行时重复查找
 */
static void convert_path_to_coords(uint32_t *patrol_path_length, s16 (*patrol_path_coords)[4])
{
    // 参数验证
    if (patrol_path_length == NULL || patrol_path_coords == NULL) {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED,
                      "convert_path_to_coords: Invalid parameters");
        return;
    }

    // 初始化输出参数
    *patrol_path_length = 0;

    // 检查是否有有效的预计算路径
    if (current_path_ptr == NULL || precomputed_path_length == 0) {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED,
                      "convert_path_to_coords: No valid path data");
        return;
    }

    // 遍历预计算路径，将position_code转换为坐标
    for (int path_idx = 0; path_idx < precomputed_path_length; path_idx++) {
        u8 target_position_code = current_path_ptr[path_idx];

        // 在work_pos数组中查找对应的坐标
        bool found = false;
        for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++) {
            if (work_pos[i][4] == target_position_code) { // work_pos[i][4]是position_code字段
                // 找到对应的工作点，复制actual_work_pos坐标
                patrol_path_coords[*patrol_path_length][0] = actual_work_pos[i][0]; // x
                patrol_path_coords[*patrol_path_length][1] = actual_work_pos[i][1]; // y
                patrol_path_coords[*patrol_path_length][2] = actual_work_pos[i][2]; // z
                patrol_path_coords[*patrol_path_length][3] = actual_work_pos[i][3]; // yaw

                (*patrol_path_length)++;
                found = true;
                break;
            }
        }

        if (!found) {
            char error_info[64];
            sprintf(error_info, "convert_path_to_coords: position_code %d not found", target_position_code);
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, error_info);
        }
    }

    // 输出转换结果
    char result_info[64];
    sprintf(result_info, "Path converted: %d coordinates ready", *patrol_path_length);
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, result_info);
}

/**
 * @brief 将返航路径转换为坐标并执行导航
 * @param return_path 返航路径position_code数组
 * @param return_length 返航路径长度
 * @note 将返航路径转换为坐标并开始执行返航导航
 */
static void convert_return_path_to_coords(const u8* return_path, int return_length)
{
    // 参数验证
    if (return_path == NULL || return_length <= 0) {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED,
                      "convert_return_path_to_coords: Invalid parameters");
        return;
    }

    // 复制返航路径数据到全局数组（避免指针指向局部变量）
    for (int i = 0; i < return_length && i < MAX_RETURN_LENGTH; i++) {
        current_return_path_data[i] = return_path[i];
    }
    current_return_path_length = return_length;
    current_return_step = 0;
    return_path_loaded = true;

    // 调试输出
    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)return_length,
                     "Return path loaded, length:");

    // 输出返航路径前几个点
    char path_info[128];
    sprintf(path_info, "Return path: %d", return_path[0]);
    for (int i = 1; i < return_length && i < 5; i++) {
        char temp[16];
        sprintf(temp, " -> %d", return_path[i]);
        strcat(path_info, temp);
    }
    if (return_length > 5) {
        strcat(path_info, " ...");
    }
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, path_info);
}

/**
 * @brief 执行返航路径导航
 * @param descent_active 是否正在进行45度降落（用于避免高度控制冲突）
 * @note 按照返航路径逐点导航到起点A9B1，支持45度降落时的高度控制协调
 */
static void execute_return_path_navigation(bool descent_active)
{
    // 检查返航路径是否已加载
    if (!return_path_loaded || current_return_path_length <= 0) {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED,
                      "execute_return_path_navigation: No return path loaded");
        return;
    }

    // 检查是否已完成所有返航点
    if (current_return_step >= current_return_path_length) {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                      "Return path navigation completed");
        return;
    }

    // 获取当前返航目标点
    u8 target_position_code = current_return_path_data[current_return_step];

    // 在work_pos数组中查找对应坐标
    bool found = false;
    for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++) {
        if (work_pos[i][4] == target_position_code) {
            // 找到目标点，设置目标位置
            if (descent_active) {
                // 45度降落进行中：只控制XY平面，不控制高度
                set_target_position(work_pos[i][0], work_pos[i][1], target_pos[2], work_pos[i][3]);
                enable_position_control(true, true, false, true); // 不开启Z轴控制
            } else {
                // 正常返航：控制XYZ和偏航
                set_target_position(work_pos[i][0], work_pos[i][1], 110, work_pos[i][3]);
                enable_position_control(true, true, true, true);
            }

            // 调试输出
            char nav_info[80];
            sprintf(nav_info, "Return nav: step %d/%d, target %d, descent=%s",
                   current_return_step + 1, current_return_path_length, target_position_code,
                   descent_active ? "ON" : "OFF");
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, nav_info);

            found = true;
            break;
        }
    }

    if (!found) {
        char error_info[64];
        sprintf(error_info, "Return navigation: position_code %d not found", target_position_code);
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, error_info);
    }
}
/**
 * @brief 使用坐标数组进行巡查点导航
 * @param patrol_step 巡查步骤索引
 * @note 直接使用patrol_path_coords数组中的坐标进行导航
 */
static void handle_patrol_navigation(int patrol_step)
{
    if (patrol_step >= 0 && patrol_step < patrol_path_length) {
        set_target_position(
            patrol_path_coords[patrol_step][0],  // x
            patrol_path_coords[patrol_step][1],  // y
            patrol_path_coords[patrol_step][2],  // z
            patrol_path_coords[patrol_step][3]   // yaw
        );
        enable_position_control(true, true, true, true);
    }
}






// ================== 降落状态重置函数 ==================
/**
 * @brief 重置降落上下文状态
 * @note 在FC_Unlock时调用，确保每次飞行周期开始时降落状态都是干净的
 *
 * 重置内容：
 * - switch_ever_pulled_down: 重置为false，清除降落权限
 * - state: 重置为LANDING_STATE_IDLE，回到空闲状态
 * - timer_ms: 重置为0，清零定时器
 *
 * 使用场景：
 * - FC_Unlock()执行前调用，确保新飞行周期状态干净
 * - 解决第二次降落失效问题的核心函数
 */
static void reset_landing_context(void)
{
    g_landing_context.switch_ever_pulled_down = false;  // 清除降落权限
    g_landing_context.state = LANDING_STATE_IDLE;       // 回到空闲状态
    g_landing_context.timer_ms = 0;                     // 清零定时器
}

// ================== 降落命令处理函数 ==================
/**
 * @brief 处理降落命令逻辑（重构版本）
 * @param ch_value 遥控器通道值
 * @note 重构后的降落命令处理，解决了原有的变量命名混乱和状态管理问题
 *
 * 重构设计要点：
 * 1. 【控制权优先级】Zigbee控制优先机制
 *    - 当zigbee_up_f == 1时，完全禁用遥控器降落命令处理
 *    - 支持zigbee控制时遥控器保持在上位（降落位置）而不触发降落
 *    - 底层程序支持随时通过遥感控制，无需应用层紧急停止逻辑
 * 2. 权限管理：实现开关下拨授权机制，满足约束条件
 *    - 默认状态（开关上位）：飞机未起飞时不执行降落
 *    - 授权机制：只有开关下拨过一次才能获得降落权限
 * 3. 状态管理：使用landing_state_t枚举进行清晰的状态转换
 *    - IDLE → ACTIVE：开始降落
 *    - ACTIVE → IDLE：中断降落
 *    - ACTIVE → TIMEOUT：超时处理（在execute_landing_sequence_v2中）
 * 4. 持续执行：确保降落序列持续执行直到超时，解决FC_Lock()不执行的问题
 *
 * 约束条件说明：
 * - 遥控器开关默认在上位（is_landing_command == true，800-1200范围）
 * - 飞机未起飞时开关在上位不应执行降落
 * - 只有开关从上位下拨到任务位置（is_mission_command == true，1700-2000范围）过一次后才能执行降落
 * - 权限获取：开关下拨到任务位置时设置switch_ever_pulled_down = true
 */
static void handle_landing_command(uint16_t ch_value)
{

    // 【优先级控制】Zigbee控制状态检查
    // 当zigbee任务激活时，完全禁用降落命令处理
    if (zigbee_up_f == 1) {
        // Zigbee控制激活中，遥控器降落命令被完全禁用
        // 底层程序支持随时通过遥感控制，无需应用层紧急停止
        return;
    }

    // 权限管理：检测开关状态变化
    if (is_mission_command(ch_value)) {
        // 开关下拨到任务位置（从上位切换到下位），获得降落权限
        g_landing_context.switch_ever_pulled_down = true;

        // 如果正在降落过程中，开关下拨则中断降落（状态转换）
        if (g_landing_context.state == LANDING_STATE_ACTIVE) {
            g_landing_context.state = LANDING_STATE_IDLE;  // 状态转换：ACTIVE → IDLE
            g_landing_context.timer_ms = 0;  // 【阶段2改进】直接重置定时器，消除全局标志位依赖
        }
        return;  // 开关在任务位置时不执行降落逻辑
    }

    // 降落命令检查：开关在上位（降落命令有效）
    if (is_landing_command(ch_value)) {
        // 权限检查：只有开关曾下拨过才能执行降落
        if (!g_landing_context.switch_ever_pulled_down) {
            // 未获得权限，不执行降落（满足约束：默认上位不降落）
            return;
        }

        // 权限验证通过，开始状态管理和持续执行逻辑

        // 降落开始逻辑：仅从空闲状态转换到活跃状态（修复：确保TIMEOUT为终态）
        if (g_landing_context.state == LANDING_STATE_IDLE) {
            g_landing_context.state = LANDING_STATE_ACTIVE;  // 状态转换：IDLE → ACTIVE
            g_landing_context.timer_ms = 0;  // 重置定时器，开始计时
        }

        // 持续执行逻辑：只要处于活跃状态就持续执行降落序列
        // 这是解决FC_Lock()不执行问题的核心：确保降落序列持续执行
        if (g_landing_context.state == LANDING_STATE_ACTIVE) {
            // 【阶段2改进】直接传递上下文，消除全局标志位和参数传递的复杂性
            // 每次UserTask_OneKeyCmd调用（50Hz）都会执行，定时器持续累加
            execute_landing_sequence_v2(&g_landing_context.timer_ms, &g_landing_context.state);
        }
    }
}


/**
 * @brief 执行降落序列（重构版本，支持状态枚举）
 * @param timer_ms 定时器指针，用于累加降落时间
 * @param state 降落状态指针，支持状态枚举管理
 * @note 重构后的降落序列执行函数，解决了FC_Lock()不执行的核心问题
 *
 * 重构改进：
 * 1. 参数类型：从u8 *land_flag改为landing_state_t *state，支持状态枚举
 * 2. 超时处理：正确设置LANDING_STATE_TIMEOUT状态
 * 3. FC_Lock()执行：确保超时时能正确执行飞控锁定
 * 4. 定时器逻辑：保持原有的20ms间隔累加，4000ms超时
 *
 * 执行逻辑：
 * - 设置降落高度为20cm，开启Z轴高度控制
 * - 定时器每20ms累加一次（50Hz调用频率）
 * - 达到4000ms（4秒）超时时执行FC_Lock()并设置TIMEOUT状态
 */
static void execute_landing_sequence_v2(u16 *timer_ms, landing_state_t *state)
{
    dadian_cnt = 0;
    target_pos[2] = LANDING_HEIGHT;  // 使用常量替换硬编码20
    Z_flag_Control(1); // 只开高度环

    // 【阶段2改进】移除全局标志位依赖，直接进行定时器管理
    // 原有的 landing_timer_reset_flag 检查逻辑已被消除
    // 定时器重置现在通过 handle_landing_command() 中的直接操作完成
    if (*timer_ms < RC_LANDING_TIMEOUT_MS) {
        BEEP_flag = 1;  // 降落过程中蜂鸣器提示
        *timer_ms += RC_TASK_INTERVAL_MS;
    } else {
        // 降落超时处理
        *state = LANDING_STATE_IDLE;  // 设置空闲状态
        all_flag_reset();  // 关闭所有环 清除所有值
        *timer_ms = 0;
				g_landing_context.switch_ever_pulled_down = 0;
        FC_Lock();  // 执行飞控锁定
        BEEP_flag = 0;  // 关闭蜂鸣器
    }
}

// ================== 任务命令处理函数 ==================
/**
 * @brief 处理任务命令逻辑（重构版本）
 * @param ch_value 遥控器通道值
 * @note 重构后的任务命令处理，优化了变量命名和状态管理
 *
 * 重构改进：
 * 1. 变量命名优化：引入语义清晰的mission_active变量
 * 2. 兼容性保障：保持can_up_f全局变量功能不变
 * 3. 双控制源支持：支持遥控器和zigbee两种控制方式
 * 4. 状态同步机制：内部状态与全局变量自动同步
 *
 * 设计说明：
 * - mission_active：内部状态管理，语义清晰
 * - mission_enabled_flag：全局变量，供execute_mission_sequence和zigbee.c使用
 * - 同步机制：mission_enabled_flag = mission_active ? 1 : 0
 */
u8 mission_enabled_flag = 0;  // 任务执行标志（全局变量，供zigbee.c使用）
u8 zigbee_up_f = 0;
static void handle_mission_command(uint16_t ch_value)
{
    static bool mission_active = false;  // 任务执行状态（内部状态管理）

    if (is_mission_command(ch_value)) {
        // 任务命令有效，检查是否需要启动任务
        if (!mission_active) {
            mission_active = true;  // 设置内部状态为活跃
            mission_step = 0;       // 开始任务流程
        }
    } else {
        // 任务命令无效，复位任务状态
        mission_active = false;
    }

    // 同步全局变量，保持与zigbee.c和execute_mission_sequence的兼容性
    mission_enabled_flag = mission_active ? 1 : 0;
}

/**
 * @brief 执行任务序列
 * @note 根据mission_enabled_flag状态决定是否执行任务状态机
 * @note 支持zigbee控制优先级，允许zigbee控制时遥控器在上位
 */
static void execute_mission_sequence(void)
{
    // 获取当前遥控器开关状态
    uint16_t ch7_value = rc_in.rc_ch.st_data.ch_[ch_7_aux3];

    // 【修正逻辑】区分控制源的安全检查
    if (is_landing_command(ch7_value)) {
        // 遥控器在降落位置（上位）
        if (zigbee_up_f == 1) {
            // Zigbee控制激活：允许遥控器在上位，不阻止zigbee任务
            // 这满足了"zigbee控制时遥控器保持开启且在上位"的需求
        } else {
            // 非Zigbee控制：遥控器在上位时停止任务（原有安全逻辑）
            mission_step = 0;
            return;
        }
    }

    // 检查任务执行条件
    if (mission_enabled_flag == 1 || zigbee_up_f == 1) {
        // 执行任务状态机（为后续重构预留接口）
        execute_mission_state_machine();
    } else {
        // 复位任务步骤
        mission_step = 0;
    }
}

// ================== 状态机辅助函数 ==================
/**
 * @brief 处理任务初始化
 * @param timer_ms 定时器指针
 * @note 重置所有标志和定时器，进入下一阶段


 */
static void handle_mission_init(u16 *timer_ms)
{
    all_flag_reset();
    *timer_ms = 0;

    // 野生动物巡查系统初始化 - QR码管理系统已移除
    // 重置动物统计数据将在新的状态机中实现

    // 重置返航路径状态（新增）
    return_path_loaded = false;
    current_return_path_length = 0;
    current_return_step = 0;

    // 清空返航路径数据
    for (int i = 0; i < MAX_RETURN_LENGTH; i++) {
        current_return_path_data[i] = 0;
    }

    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                  "Return path system initialized");

    mission_step += 1;
}

/**
 * @brief 处理起始位置设置和工作点计算
 * @param timer_ms 定时器指针
 * @note 获取当前位置作为起始点，计算实际工作点坐标
 */
static void handle_home_position_setup(void)
{
    // 切换真程控模式
    home_pos[0] = mid360.pose_x_cm;
    home_pos[1] = mid360.pose_y_cm; // 获取起点位置坐标
    home_pos[3] = mid360.pose_yaw;
    if (is_radar_data_abnormal(home_pos[0], home_pos[1])) {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "laser error!");
        mission_step = 0;
        return;
    }

    // 计算实际工作点坐标（应用漂移补偿）
    calculate_actual_work_positions();
    mission_step += LX_Change_Mode(2);
}

/*
 * @brief 处理等待
 * @param timer_ms 定时器指针
 * @param time 等待时间(毫秒)
 * @return true: 延时完成, false: 延时进行中
 * @note 纯粹的延时函数，不负责状态推进，返回延时完成状态
 */
 bool handle_wait(u16 *timer_ms, u16 time)
{
    if (*timer_ms < time) {
        *timer_ms += RC_TASK_INTERVAL_MS;
        return false; // 延时进行中
    } else {
        *timer_ms = 0;
        return true;  // 延时完成
    }
}
/**
 * @brief 执行降落操作
 * @param timer_ms 定时器指针
 * @param time 到达降落高度后的稳定延时时间
 * @return true: 降落完成, false: 降落进行中
 * @note 先检测是否到达降落高度，然后进行稳定延时，最后执行上锁操作
 */
 bool land(u16 *timer_ms, u16 time)
{
    // 设置目标降落高度并开启Z轴控制环
    target_pos[2] = LANDING_HEIGHT;
    Z_flag_Control(1); // 只开高度环

    // 检测是否到达降落高度（使用Z轴位置检测逻辑）
    if (is_z_position_reached()) {
        // 到达降落高度后进行稳定延时
        if (handle_wait(timer_ms, time)) {
            // 延时完成后执行降落完成操作
            all_flag_reset();  // 关闭所有环 清除所有值
            FC_Lock();
            return true;  // 降落完成
        }
    }

    return false; // 降落进行中
}
/**
 * @brief 处理返回起始点
 * @param timer_ms 定时器指针
 * @note 飞行器返回起始位置并开启所有控制环
 */
static void handle_return_home(void)
{
    set_target_position(home_pos[0], home_pos[1], 110, home_pos[3]);
    enable_position_control(true, true, true, true);
}






/**
 * @brief 设置目标位置
 * @param x X坐标
 * @param y Y坐标
 * @param z Z坐标（高度）
 * @param yaw 偏航角
 * @note 统一的位置设置接口，避免重复代码
 */
static void set_target_position(s16 x, s16 y, s16 z, s16 yaw)
{
    target_pos[0] = x;
    target_pos[1] = y;
    target_pos[2] = z;
    target_pos[3] = yaw;
}

/**
 * @brief 启用位置控制
 * @param xy 是否启用XY位置控制
 * @param z 是否启用Z位置控制
 * @param yaw 是否启用偏航角控制
 * @note 统一的控制环管理接口
 */
static void enable_position_control(bool x, bool y, bool z, bool yaw)
{
    if (x && y) XY_flag_Control(1, 1);  // 同时开启X和Y轴控制
		if(x && !y) XY_flag_Control(1, 0); // 只开启X
		if(!x && y) XY_flag_Control(0, 1); // 只Y轴控制
    if (z) Z_flag_Control(1);
    if (yaw) YAW_flag_Control(1);
}








/**
 * @brief 处理工作点导航
 * @param work_point_index 工作点索引 (0-4)
 * @note 导航到指定工作点并等待到达
 */
void handle_work_point_navigation(u8 work_point_index)
{
//    // 根据工作点索引设置不同的高度
//    s16 work_height = (work_point_index < 3) ? WORK_HEIGHT_HIGH : WORK_HEIGHT_LOW;

    set_target_position(
        actual_work_pos[work_point_index][0],
        actual_work_pos[work_point_index][1],
        actual_work_pos[work_point_index][2],
        actual_work_pos[work_point_index][3]
    );
    enable_position_control(true, true, true, true);

}


/**
 * @brief 执行任务状态机
 * @note 重构后的状态机，包含13个case的清晰处理逻辑
 */
// ================== 优化后的任务状态机 ==================
static void execute_mission_state_machine(void)
{
    static u16 mission_timer_ms = 0;

    switch(mission_step) {
        case 0: // 初始化阶段
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "Mission init...");
            handle_mission_init(&mission_timer_ms);
            break;

        case 1: // 获取起始位置和计算工作点
            handle_home_position_setup();
            break;

        case 2: // 路径规划阶段
        {
            reset_landing_context();

            // 使用缓存的禁飞区数据（性能优化）
            u8 no_fly_zones[3];
            u8 no_fly_count = zigbee_get_no_fly_zones(no_fly_zones);

            // 查找预计算路径
            if (no_fly_count == 3) {
                u8 path_buffer[MAX_PATH_LENGTH];
                int path_length = find_precomputed_path(no_fly_zones, path_buffer);

                if (path_length > 0) {
                    // 复制路径到全局缓冲区
                    for (int i = 0; i < path_length; i++) {
                        precomputed_path_buffer[i] = path_buffer[i];
                    }
                    current_path_ptr = precomputed_path_buffer;
                    precomputed_path_length = path_length;
                    current_path_index = 0;
                    current_patrol_step = 0;

                    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                                  "Path loaded successfully");

                    // 将预计算路径转换为坐标数组
                    uint32_t temp_patrol_length = 0;
                    convert_path_to_coords(&temp_patrol_length, patrol_path_coords);
                    patrol_path_length = (int)temp_patrol_length; // 更新全局变量
                    current_patrol_step = 0; // 重置步骤索引
										mission_step += FC_Unlock();
	//					mission_step += 1; // 要调试一定要记得取消注释，注意安全
							AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "Takeoff!");
                } else {
                    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED,
                                  "No path found for current no-fly zones");
                    mission_step = 0;  // 中止任务
                    return;
                }
            } 
            break;
        }
				
				case 3:
				{
					
							handle_return_home();
							if (!handle_wait(&mission_timer_ms, 1500)) {
								 return;
								}
							mission_step += 1;
       
				}break;

        // 巡查点导航状态（重构后 - 支持动态路径长度）
        case 4:
        {
            static int internal_patrol_step = 0;  // 内部巡查步骤计数器

            // 第一次进入时初始化
            static bool patrol_initialized = false;
            if (!patrol_initialized) {
                internal_patrol_step = 0;
                patrol_initialized = true;
            }
            
            // 检查是否还有巡查点
            if (internal_patrol_step >= patrol_path_length) {
                // 所有巡查点已完成
                u32 total_mission_ms = GetSysRunTimeMs() - mission_start_time_ms;
                AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                              "=== MISSION COMPLETED ===");

                char completion_stats[128];
                sprintf(completion_stats, "Total mission time: %lu ms (%.2f seconds)",
                        total_mission_ms, total_mission_ms / 1000.0f);
                AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, completion_stats);

                // 固定跳转到返航状态（case 63是返航初始化的固定状态）
                patrol_initialized = false;  // 重置标志，为下次任务准备
                mission_step = 63;  // 跳转到返航状态
                break;
            }

            // 导航到当前巡查点
            set_target_position(
                patrol_path_coords[internal_patrol_step][0],
                patrol_path_coords[internal_patrol_step][1],
                patrol_path_coords[internal_patrol_step][2],
                patrol_path_coords[internal_patrol_step][3]
            );
            enable_position_control(true, true, true, true);

            // 检查是否到达位置
            if (is_position_reached() ) {  //&& is_yaw_reached() && is_z_position_reached()
                // 位置到达后进行稳定延时
                if (!handle_wait(&mission_timer_ms, RC_MISSION_DELAY_MS)) {
                    return;
                }

                
              
                    // 获取当前巡查点的position_code
                    u8 current_position_code = 0;
                    if (current_path_ptr != NULL && internal_patrol_step < precomputed_path_length) {
                        current_position_code = current_path_ptr[internal_patrol_step];
                    }
										
										
                // 野生动物识别处理：当ID为1-5时表示识别到动物
                if (maixcam.id >= 1 && maixcam.id <= 5 && maixcam.count > 0)
                {
	                    // 检查当前位置是否已发送过动物数据
                    if (!is_position_code_sent(current_position_code)) {
                        // 首次发送
                        zigbee_send_screen_animal(current_position_code, maixcam.id, maixcam.count);
                        mark_position_code_sent(current_position_code);
                    } else {
                        // 跳过重复发送
                        char skip_info[64];
                        sprintf(skip_info, "Animal data already sent for pos_code: %d (skipping duplicate)",
                                current_position_code);
                        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_YELLOW, skip_info);
                    }
                    // ID直接对应动物类型：1=象，2=虎，3=狼，4=猴，5=孔雀
                    // 统计功能由地面站实现，飞控端只发送数据
									if(maixcam.x<30)
									{
										jiguang(1,0,0);
									}else if(  30<maixcam.x && maixcam.x<110)
									{
										jiguang(0,1,0);
									}else{
										jiguang(0,0,1);
									}
                }
								else
								{
									jiguang(0,1,0);
								}
                    // 发送巡查数据
                    char point_info[64];
                    sprintf(point_info, "Patrol point %d complete (pos_code: %d)",
                            internal_patrol_step + 1, current_position_code);
                    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, point_info);

                    // 添加巡查点完成标记
                    int work_pos_index = find_work_pos_index_by_position_code(current_position_code);
                    if (work_pos_index >= 0) {
                        mark_patrol_point_completed(work_pos_index);
                    }
                    // 进入下一个巡查点（重构后：使用内部计数器）
                    internal_patrol_step++;  // 增加内部计数器

                    // 检查是否还有更多巡查点
                    if (internal_patrol_step >= patrol_path_length) {
                        // 所有巡查点已完成，直接跳转到返航
                        u32 total_mission_ms = GetSysRunTimeMs() - mission_start_time_ms;
                        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                                      "=== ALL PATROL POINTS COMPLETED ===");

                        char completion_stats[128];
                        sprintf(completion_stats, "Total patrol points: %d, mission time: %lu ms (%.2f seconds)",
                                patrol_path_length, total_mission_ms, total_mission_ms / 1000.0f);
                        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, completion_stats);

                        // 固定跳转到返航状态（case 63是返航初始化的固定状态）
                        mission_step = 63;  // 跳转到返航状态
                    }
										
            } else {
                // 位置未到达时，确保蜂鸣器关闭
                BEEP_flag = 0;
            }
            break;
        }

        case 63: // 返航初始化 - 加载预计算返航路径
        {
            // 获取当前禁飞区信息
            u8 no_fly_zones[3];
            zigbee_get_no_fly_zones(no_fly_zones);

            // 加载预计算返航路径
            if (!return_path_loaded) {
                u8 return_path[MAX_RETURN_LENGTH];
                u8 return_length = find_precomputed_return_path(no_fly_zones, return_path);

                if (return_length > 0) {
                    // 转换返航路径为坐标
                    convert_return_path_to_coords(return_path, return_length);
                    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                                  "Precomputed return path loaded successfully");
                } else {
                    // 路径加载失败，使用默认返航策略
                    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED,
                                  "Return path load failed, using direct return");
                    return_path_loaded = true; // 标记为已处理，避免重复尝试
                }
            }

            // 状态转换到导航执行阶段
            mission_step = 64;
            break;
        }

        case 64: // 返航导航阶段
        {
            // 计算当前位置到起点的距离
            float current_x = (float)mid360.pose_x_cm;
            float current_y = (float)mid360.pose_y_cm;
            float xy_distance = sqrtf(current_x * current_x + current_y * current_y);

            // 检查45度降落触发条件
            if (xy_distance <= 110.0f) {
                AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                              "45deg descent trigger condition met, switching to descent mode");
                mission_step = 65; // 转入45度降落状态
                break;
            }

            // 执行返航导航
            if (return_path_loaded) {
                execute_return_path_navigation(false);

                // 检查当前返航点是否到达
                if (is_position_reached()) {
                    current_return_step++;

                    // 调试输出返航进度
                    char progress_info[64];
                    sprintf(progress_info, "Return step %d/%d completed",
                           current_return_step, current_return_path_length);
                    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_DEFAULT, progress_info);
                }

                // 检查返航是否完成（但距离仍>110cm）
                if (current_return_step >= current_return_path_length) {
                    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                                  "Return path completed but distance > 110cm, direct approach");
                    handle_return_home(); // 直接飞向起点
                }
            } 

            break;
        }

        case 65: // 45度角降落执行
        {
            static bool descent_initialized = false;

            // 初始化45度降落（只执行一次）
            if (!descent_initialized) {
                if (start_45deg_descent()) {
                    descent_initialized = true;
                    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                                  "45deg descent initialized successfully");
                } 
            }

            // 执行45度降落控制
            if (control_45deg_descent(false)) {
                AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                              "45deg descent phase completed");
                descent_initialized = false; // 重置标志，为下次任务准备
                mission_step = 67; // 转入最终降落
            }

            break;
        }

        case 67: // 最终降落
        {
            BEEP_flag = 1;

            // 等待200ms确保飞机稳定
            if (!handle_wait(&mission_timer_ms, 200)) {
                return;
            }

            // 执行飞控锁定
            FC_Lock();
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                          "Landing complete!");

            // 转入任务结束状态
            mission_step = 68;
            break;
        }

        case 68: // 任务结束状态
        {
            static bool stats_printed = false;

            if (!stats_printed) {
                all_flag_reset();
                LED_f = 0;
                BEEP_flag = 0;
								jiguang(0,0,0);

                // 【关键修复】重置任务状态标志位，允许新任务启动
                mission_enabled_flag = 0;  // 重置任务执行标志
                zigbee_up_f = 0;           // 重置Zigbee任务标志
                mission_step = 0;          // 重置任务步骤到初始状态

                int completed, total;
                uint32_t elapsed_ms;
                get_patrol_statistics(&completed, &total, &elapsed_ms);

                char final_stats[128];
                sprintf(final_stats, "Mission complete! %d/%d points patrolled in %lu seconds",
                        completed, total, elapsed_ms / 1000);
                AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, final_stats);

                // 任务状态重置确认信息
                AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_DEFAULT,
                              "Task flags reset - Ready for new mission");

                stats_printed = true;
            }

            // 任务完全结束，保持在当前状态等待新任务
            break;
        }
    }
}



/*
 * ================== 打点程序 ==================
 * 功能：记录工作点坐标，用于任务规划和路径设置
 * 状态：当前已注释，可根据需要启用
 * 使用方法：
 *   - 拨杆拨下(1700-2000)：记录当前位置为工作点
 *   - 拨杆拨上(800-1200)：复位打点状态和蜂鸣器
 * 注意事项：
 *   - 只有在非任务执行状态下才能进行打点操作
 *   - 每次打点会自动递增工作点计数器
 *   - 打点时会触发蜂鸣器提示用户
 */
/*
static void handle_dadian_command(uint16_t ch_value)
{
    // 没有执行一键任务时 且 打点拨杆拨下时
    if (is_dadian_down_command(ch_value) && (mission_enabled_flag == 0)) {
        if (dadian_f == 0) {
            // 记录当前位置为工作点
            work_pos[dadian_cnt][0] = mid360.pose_x_cm;
            work_pos[dadian_cnt][1] = mid360.pose_y_cm;
            work_pos[dadian_cnt][2] = WORK_HEIGHT_HIGH; // 使用常量定义高度
            work_pos[dadian_cnt][3] = 0; // 默认偏航角

            BEEP_flag = 1; // 蜂鸣器提示
            dadian_cnt++;
            dadian_f = 1; // 标记已执行
        }
    }
    // 没有执行一键任务时 且 打点拨杆拨上时
    else if (is_landing_command(ch_value) && (mission_enabled_flag == 0)) {
        yuyin_flag = 0x00;
        dadian_f = 0; // 复位打点状态
        BEEP_flag = 0; // 关闭蜂鸣器
    }
}
*/

void UserTask_OneKeyCmd(void)
{
    // 早期返回模式，减少嵌套
    if (!is_rc_signal_valid()) return;

    uint16_t ch7_value = rc_in.rc_ch.st_data.ch_[ch_7_aux3];
	uint16_t ch8_value = rc_in.rc_ch.st_data.ch_[ch_8_aux4];
    // 处理降落命令
    handle_landing_command(ch7_value);

    // 处理打点程序（注释状态保留）
    // handle_dadian_command(ch8_value);

    // 处理任务命令
    handle_mission_command(ch7_value);

//    // 执行任务序列
////		if(task_times==0)
////		{
			execute_mission_sequence();

			// 导航功能已集成到路径规划模块中
////		}


}

// ================== 野生动物巡查路径规划集成函数 ==================





// ================== 野生动物巡查点管理函数 ==================

/**
 * @brief 获取下一个巡查点（严格按预计算路径执行）
 * @return 巡查点索引，无可用点时返回-1
 * @note 必须按照预计算路径序列执行，无预计算路径时任务中止
 */
int get_next_patrol_point(void)
{
    // 检查是否有预计算路径引用
    if (precomputed_path_length == 0 || current_path_ptr == NULL) {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED,
                      "CRITICAL ERROR: No precomputed path available - Mission aborted");
        return -1;
    }

    // 检查路径执行是否完成
    if (current_path_index >= precomputed_path_length) {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                      "Precomputed path execution completed");
        return -1;
    }

    // 获取当前路径序列中的position_code（通过指针直接访问Flash）
    u8 target_position_code = current_path_ptr[current_path_index];

    // 在work_pos数组中查找对应的索引
    for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++) {
        if (work_pos[i][4] == target_position_code) { // work_pos[i][4]是position_code字段
            // 找到对应的工作点
            char debug_info[64];
            sprintf(debug_info, "Path step %d: position_code %d at index %d",
                   current_path_index + 1, target_position_code, i);
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, debug_info);

            current_path_index++; // 为下次查找准备
            return i;
        }
    }

    // 没找到对应的position_code，这是一个严重错误
    char error_info[64];
    sprintf(error_info, "CRITICAL ERROR: position_code %d not found in work_pos", target_position_code);
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, error_info);

    // 跳过这个无效的position_code，继续下一个
    current_path_index++;
    return get_next_patrol_point(); // 递归查找下一个
}


/**
 * @brief 根据position_code查找work_pos数组索引
 * @param position_code 位置代码
 * @return work_pos数组索引，未找到返回-1
 */
int find_work_pos_index_by_position_code(u8 position_code)
{
    for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++) {
        if (work_pos[i][4] == position_code) { // work_pos[i][4]存储position_code
            return i;
        }
    }
    return -1; // 未找到
}

/**
 * @brief 标记巡查点为已完成
 * @param index 巡查点索引
 * @note 标记指定索引的巡查点为已完成状态
 */
void mark_patrol_point_completed(int index)
{
    if (index >= 0 && index < WORK_POINT_ARRAY_SIZE) {
        patrol_point_status[index] = true;

        // 调试输出
        char debug_str[64];
        sprintf(debug_str, "Patrol point %d completed (pos_code: %d)",
                index, work_pos[index][4]);
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, debug_str);
    }
}

/**
 * @brief 检查巡查点是否已完成
 * @param index 巡查点索引
 * @return true: 已完成, false: 未完成
 */
bool is_patrol_point_completed(int index)
{
    if (index >= 0 && index < WORK_POINT_ARRAY_SIZE) {
        return patrol_point_status[index];
    }
    return false;
}

/**
 * @brief 检查position_code是否已发送动物数据
 * @param position_code 位置代码
 * @return true: 已发送, false: 未发送
 * @note 线性搜索已发送列表，时间复杂度O(n)，但n很小（最多63）
 */
bool is_position_code_sent(u8 position_code)
{
    for (int i = 0; i < sent_count; i++) {
        if (sent_position_codes[i] == position_code) {
            return true;
        }
    }
    return false;
}

/**
 * @brief 标记position_code已发送动物数据
 * @param position_code 位置代码
 * @note 将position_code添加到已发送列表，包含重复检查和边界保护
 */
void mark_position_code_sent(u8 position_code)
{
    // 检查是否已存在和数组边界
    if (!is_position_code_sent(position_code) && sent_count < MAX_SENT_POSITIONS) {
        sent_position_codes[sent_count++] = position_code;

        // 调试输出
        char debug_str[64];
        sprintf(debug_str, "Animal data sent for pos_code: %d (first time)", position_code);
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, debug_str);
    }
}

/**
 * @brief 检查巡查是否完成
 * @return true: 完成, false: 未完成
 * @note 检查所有有效工作点（非禁飞区）是否已访问
 */
bool is_patrol_complete(void)
{
    int total_valid_points = 0;
    int completed_points = 0;

    // 统计有效巡查点和已完成点数量
    for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++) {
        if (work_pos[i][5] == 0) { // 非禁飞区
            total_valid_points++;
            if (patrol_point_status[i]) {
                completed_points++;
            }
        }
    }

    return (completed_points >= total_valid_points && total_valid_points > 0);
}

/**
 * @brief 检查任务超时
 * @return true: 超时, false: 未超时
 * @note 检查任务是否超过300秒（5分钟）限制
 */
bool check_mission_timeout(void)
{
    uint32_t current_time = GetSysRunTimeMs();
    uint32_t elapsed_time = current_time - mission_start_time_ms;

    // 300秒 = 300000毫秒
    if (elapsed_time > 300000) {
        char debug_str[64];
        sprintf(debug_str, "Mission timeout: %lu ms elapsed", elapsed_time);
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, debug_str);
        return true;
    }

    // 每30秒输出一次时间提醒
    static uint32_t last_reminder_time = 0;
    if (elapsed_time - last_reminder_time > 30000) {
        last_reminder_time = elapsed_time;
        char debug_str[64];
        sprintf(debug_str, "Mission time: %lu s / 300 s", elapsed_time / 1000);
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_DEFAULT, debug_str);
    }

    return false;
}







/**
 * @brief 获取巡查统计信息
 * @param completed 已完成点数量指针
 * @param total 总有效点数量指针
 * @param elapsed_ms 已用时间毫秒指针
 * @note 获取当前巡查任务的基本统计信息
 */
void get_patrol_statistics(int *completed, int *total, uint32_t *elapsed_ms)
{
    int total_valid = 0;
    int completed_count = 0;

    // 统计有效点和已完成点
    for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++) {
        if (work_pos[i][5] == 0) { // 非禁飞区
            total_valid++;
            if (patrol_point_status[i]) {
                completed_count++;
            }
        }
    }

    if (completed) *completed = completed_count;
    if (total) *total = total_valid;
    if (elapsed_ms) *elapsed_ms = GetSysRunTimeMs() - mission_start_time_ms;
}



// ================== 45°角降落函数实现 ==================

/**
 * @brief 开始45°角降落
 * @return true: 初始化成功, false: 初始化失败
 * @note 计算45度角降落轨迹参数，实现XY合速度=Z下降速度的45度角下降
 */
static bool start_45deg_descent(void)
{
	
		LED_f = 1;
    // 获取当前位置信息
    descent_start_height = (float)g_tfmini_sensor.distance_cm;;


    // 检查当前高度是否合理
    if (descent_start_height < 50.0f) {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "Current height too low for 45deg descent");
        return false;
    }

    // 计算降落距离（Z轴方向）
    descent_distance = descent_start_height ;

    // 设置45°降落参数
    descent_angle = 45.0f;                    // 45度角
    descent_speed = 30.0f;                    // 降落速度 20cm/s（XY和Z方向相等）

    return true;
}

/**
 * @brief 控制45°角降落过程
 * @param return_navigation_active 是否正在进行返航导航（用于避免XY控制冲突）
 * @return true: 降落完成, false: 降落进行中
 * @note 实现45度降落：在返航导航时只控制Z轴，独立降落时控制XYZ
 */
static bool control_45deg_descent(bool return_navigation_active)
{
    // 获取当前位置
    float current_x = (float)mid360.pose_x_cm;
    float current_y = (float)mid360.pose_y_cm;
    float current_z = (float)g_tfmini_sensor.distance_cm;

    // 计算到起点(0,0)的XY平面距离
    float xy_distance = sqrtf(current_x * current_x + current_y * current_y);

    // 检查是否到达45°降落触发点（距离起点110cm）
    if (xy_distance <= 110.0f) {

        // 45°降落核心逻辑：保持 高度 = XY距离，且速度相等
        // 理想情况：XY距离应该等于高度，实现真正的45度角
        float ideal_height = xy_distance; // 45度角：高度 = XY距离

        // 计算每个控制周期的移动距离（固定步长，避免时间同步问题）
        float move_distance = 10.0f; // 每次移动1.5cm，经验值，可调整

        // 计算XY方向的单位向量（指向原点）
        float unit_x = 0, unit_y = 0;
        if (xy_distance > 0.1f) { // 避免除零
            unit_x = -current_x / xy_distance;
            unit_y = -current_y / xy_distance;
        }

        // 计算目标位置：XY和Z同步移动，保持45度角
        float target_x = current_x + unit_x * move_distance;
        float target_y = current_y + unit_y * move_distance;
        float target_z = current_z - move_distance; // Z方向下降相同距离

        // 边界检查：确保不会超出合理范围
        if (target_z < 0) target_z = 0;

        // 设置目标位置
        target_pos[0] = 0;//(s16)target_x;
        target_pos[1] = 0;//(s16)target_y;
        target_pos[2] = 0;//(s16)target_z;
        target_pos[3] = 0; // 保持偏航角为0

        // 开启所有控制环
        enable_position_control(true, true, true, true);

        // 调试输出：显示45度角轨迹信息
        static uint32_t last_trajectory_debug = 0;
        uint32_t current_time = GetSysRunTimeMs();
        if (current_time - last_trajectory_debug > 500) { // 每500ms输出一次
            char debug_str[128];
            sprintf(debug_str, " XY_juli=%.1fcm,Z=%.1fcm,angle=%.1fdeg",
										xy_distance,
										current_z,
                    atan2f(current_z, xy_distance) * 180.0f / 3.14159f);
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_DEFAULT, debug_str);
            last_trajectory_debug = current_time;
        }

        // 检查45度降落完成条件
        if (current_z <= 15.0f || xy_distance <= 5.0f) {
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "45deg descent phase complete");
            return true; // 45°降落完成
        }
    }
		
    return false; // 降落进行中
}




