/*==========================================================================
 * 版权    ：米醋电子工作室
 * 创建时间：2025-07-31
 * 作者    ：Alex (Engineer)
 * 功能描述：预计算路径存储模块 - 头文件
 * 编码格式：UTF-8
 * 
 * 说明：
 * 本模块包含基于Dijkstra算法预计算的92种禁飞区组合的最优路径数据。
 * 提供高效的路径查找功能，响应时间<1ms，替代单片机实时路径规划。
===========================================================================*/
#ifndef __PATH_STORAGE_H
#define __PATH_STORAGE_H
#include "SysConfig.h"

/*==========================================================================
 * 常量定义
===========================================================================*/
#define MAX_NO_FLY_ZONES        3       // 最大禁飞区数量
#define MAX_PATH_LENGTH         70      // 最大巡查路径长度
#define MAX_RETURN_LENGTH       25      // 最大返航路径长度
#define PRECOMPUTED_PATH_COUNT  92      // 预计算路径总数

/*==========================================================================
 * 数据结构定义
===========================================================================*/
/**
 * @brief 预计算路径数据结构（扩展版本，包含返航路径）
 * @note 每个结构体占用89字节，总计约7.0KB存储空间
 */
typedef struct {
    u8 no_fly_zones[MAX_NO_FLY_ZONES];     // 禁飞区position_code数组
    u8 path_length;                        // 巡查路径长度（通常为60）
    u8 path_sequence[MAX_PATH_LENGTH];     // 巡查路径序列（position_code数组）
    u8 return_length;                      // 返航路径长度（新增）
    u8 return_sequence[MAX_RETURN_LENGTH]; // 返航路径序列（新增）
} precomputed_path_t;

/*==========================================================================
 * 函数声明
===========================================================================*/
/**
 * @brief 查找预计算巡查路径
 * @param no_fly_zones 禁飞区position_code数组（3个元素）
 * @param output_path 输出路径缓冲区（至少60字节）
 * @return 路径长度，失败时返回0
 * @note 查找时间<1ms，线性搜索92个条目
 */
u8 find_precomputed_path(const u8 no_fly_zones[3], u8 output_path[MAX_PATH_LENGTH]);

/**

/**
 * @brief 查找预计算的最优返航路径
 * @param no_fly_zones 禁飞区position_code数组（3个元素）
 * @param output_return_path 输出返航路径数组（调用者分配）
 * @return 返航路径长度，0表示未找到
 */
u8 find_precomputed_return_path(const u8 no_fly_zones[3], u8 output_return_path[MAX_RETURN_LENGTH]);

/**
 * @brief 获取预计算路径统计信息
 * @param total_paths 输出总路径数
 * @param avg_path_length 输出平均路径长度
 * @return 0成功，失败时返回非0值
 */
u8 get_path_statistics(u16* total_paths, u8* avg_path_length);

/**
 * @brief 验证禁飞区组合的有效性
 * @param no_fly_zones 禁飞区position_code数组（3个元素）
 * @return 1有效，0无效
 * @note 检查禁飞区是否为3个连续点且不包含A9B1起点
 */
u8 validate_no_fly_zones(const u8 no_fly_zones[3]);

#endif /* __PATH_STORAGE_H */
