#ifndef __USER_TASK_H
#define __USER_TASK_H

#include "SysConfig.h"
#include <stdbool.h>




// ================== 状态枚举定义 ==================
/**
 * @brief 降落状态枚举（重构新增）
 * @note 遵循项目mid360_status_t的命名和结构风格，使用下划线命名规范
 *
 * 状态转换说明：
 * IDLE → ACTIVE：检测到降落命令且有权限时开始降落
 * ACTIVE → IDLE：降落过程中开关下拨或手动停止
 * ACTIVE → TIMEOUT：降落超时（4秒）后自动转换
 * TIMEOUT：超时状态，执行FC_Lock()锁定飞控
 * COMPLETED：降落完成状态（预留，当前版本未使用）
 */
typedef enum {
    LANDING_STATE_IDLE = 0,        // 降落空闲状态：等待降落命令
    LANDING_STATE_ACTIVE,          // 降落进行中：正在执行降落序列
    LANDING_STATE_TIMEOUT,         // 降落超时状态：4秒超时后执行FC_Lock()
    LANDING_STATE_COMPLETED        // 降落完成状态：预留状态（未使用）
} landing_state_t;

/**
 * @brief 降落上下文管理结构体
 * @note 封装降落功能相关的所有状态信息，替代分散的静态变量和全局标志
 *
 * 设计目标：
 * 1. 消除全局标志位依赖，提高代码可读性和可维护性
 * 2. 将相关状态集中管理，便于调试和状态追踪
 * 3. 支持直接参数传递，减少函数间的隐式耦合
 *
 * 使用方式：
 * - 在 handle_landing_command() 中直接操作上下文状态
 * - 在 execute_landing_sequence_v2() 中通过参数传递接收上下文
 * - 替代原有的 landing_timer_reset_flag 全局标志位机制
 */
typedef struct {
    bool switch_ever_pulled_down;    // 开关是否曾下拨过（权限管理）
    landing_state_t state;           // 当前降落状态
    u16 timer_ms;                    // 降落定时器（毫秒）
} landing_context_t;

// 巡查点导航重构说明：
// 原有硬编码的 case 3-62 已重构为动态的 case 3
// 支持任意长度的巡查路径，通过 patrol_path_length 变量控制
// 保持原有的简单遍历逻辑，提高代码可维护性

// 外部变量声明
extern s16  home_pos[4];           // 起始位置坐标
extern s16  work_pos[63][6];       // 工作点坐标数组 (7×9网格) - 移除order字段
extern u8   BEEP_flag;             // 蜂鸣器标志
extern u8   mission_enabled_flag; // 任务执行标志（重构后的语义化命名）
extern u8   zigbee_up_f;           // Zigbee任务执行标志
extern u8   LED_f;                 // LED控制标志

// 函数声明
void UserTask_OneKeyCmd(void);

// 导航相关函数声明（供Path_Navigator使用）
void handle_work_point_navigation(u8 work_point_index);
bool is_position_reached(void);
bool is_yaw_reached(void);
bool handle_wait(u16 *timer_ms, u16 time);
bool land(u16 *timer_ms, u16 time);
extern u8 mission_step;
extern inline bool is_z_position_reached(void);

// 预计算路径相关函数声明
int get_next_patrol_point(void);

/**
 * @brief 将预计算路径转换为坐标数组（参数化版本）
 * @param patrol_path_length 输出巡航路径长度的指针
 * @param patrol_path_coords 输出巡航路径坐标数组的指针
 * @return bool 转换是否成功
 * @note 明确输入输出关系，提高代码可读性和可维护性
 */
bool convert_path_to_coords_ex(uint32_t *patrol_path_length, s16 (*patrol_path_coords)[4]);

// 巡查点导航重构完成，无需额外的函数声明

/**
 * @brief 加载巡检路径引用（指针模式）
 * @param path_sequence Flash中路径序列的const指针
 * @param path_length 路径长度
 * @return 加载成功返回true，失败返回false
 * @note 性能优化版本，使用指针引用替代数组复制，节省60字节RAM
 */
bool load_patrol_reference(const u8* path_sequence, int path_length);

// 路径规划相关变量声明
extern int current_patrol_index;       // 当前巡查索引
extern u32 mission_start_time_ms;      // 任务开始时间
extern  void LED_PWM_Control(void);
extern void jiguang(u8 a,u8 b,u8 c);
#endif
