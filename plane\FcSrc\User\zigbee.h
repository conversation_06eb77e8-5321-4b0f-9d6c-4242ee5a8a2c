#ifndef __ZIGBEE_H
#define __ZIGBEE_H

#include "SysConfig.h"
#include <stdbool.h>  // 包含bool类型定义
#include "path_storage.h"  // 包含预计算路径存储模块

/*******************************************************
    2025年电赛H题 - 野生动物巡查系统通信协议

    CMD=0x01 禁飞区设置协议：
    格式：AA FF 01 [位置1] [位置2] [位置3] EA
    说明：固定传输3个连续方格的位置代码
    示例：AA FF 01 32 33 34 EA (A2B3, A3B3, A4B3)

    CMD=0x02 开始巡检起飞命令：
    格式：AA FF 02 [任务ID] EA
    说明：启动巡检任务，设置zigbee_up_f=1

    CMD=0x03 校准命令：
    格式：AA FF 03 [校准ID] EA
    说明：坐标系校准功能（ID=1~3对应不同校准点）
********************************************************/

// 串口屏协议命令定义
#define SCREEN_CMD_NO_FLY_ZONE    0x01  // 禁飞区设置命令
#define SCREEN_CMD_PATROL         0x02  // 开始巡检起飞命令
#define SCREEN_CMD_CALIBRATION    0x03  // 校准命令

// 禁飞区相关常量定义 - 根据2025年电赛H题要求
#define MAX_NO_FLY_ZONES          3     // 最大禁飞区数量（题目规定为三个连续方格）
#define COMPETITION_NO_FLY_ZONES  3     // 竞赛标准禁飞区数量

// 串口屏数据结构
typedef struct
{
    u8 cmd;                  // 串口屏命令类型 (0x01/0x02/0x03)
    u8 id;                   // 串口屏ID (禁飞区命令=3, 巡检命令=任务id, 校准命令=校准点id)
    u8 data_valid;           // 数据有效标志
} __attribute__((packed)) screen_info;

// 全局变量声明
extern screen_info screen;    // 串口屏相关数据

// 串口屏协议相关函数声明
void screen_receiver_GetOneByte(const u8 linktype, const u8 data);       // 串口屏协议接收函数
void zigbee_send_screen_data(u8 position_id, u8 type_id);               // 串口屏数据发送函数
void zigbee_send_screen_animal(u8 position_id, u8 type_id, u8 count);   //三字节发送
void zigbee_screen_data_handler(const u8 cmd, const u8 id);             // 串口屏数据处理回调函数

// MID360校准相关函数声明（已移除未使用的函数）

// 坐标系校准相关函数声明
void zigbee_set_calibration_point_1(s16 mid360_x, s16 mid360_y);        // 设置第一个校准点
bool zigbee_execute_two_point_calibration(s16 mid360_x2, s16 mid360_y2); // 执行两点校准
void zigbee_apply_coordinate_calibration(void);                         // 应用校准参数
void zigbee_reset_coordinate_calibration(void);                         // 重置校准参数

// 简化导航相关函数声明
void simple_navigation_update(void);                                    // 简化导航状态更新

// 禁飞区处理相关函数声明
void zigbee_process_no_fly_zones(const u8* position_data, u8 count);    // 禁飞区数据处理函数（简化版）
bool zigbee_validate_continuous_no_fly_zones(const u8* position_data, u8 count); // 连续性验证函数
u8 zigbee_get_no_fly_zones(u8* no_fly_zones);                           // 获取缓存的禁飞区数据

// 巡查状态重置函数声明
void reset_patrol_order(void);                                          // 重置巡查状态

// 野生动物巡查系统函数声明已删除
// 统计功能由地面站实现，飞控端只负责发送识别数据

#endif
